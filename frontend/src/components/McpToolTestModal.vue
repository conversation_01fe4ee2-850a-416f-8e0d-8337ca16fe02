<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Test Tool: {{ tool.name }}</h3>
            <p class="text-sm text-gray-600">{{ tool.description }}</p>
          </div>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Input Panel -->
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">Input Parameters</h4>
            
            <!-- Schema Display -->
            <div v-if="tool.inputSchema" class="mb-4 p-3 bg-gray-50 rounded-lg">
              <h5 class="text-sm font-medium text-gray-700 mb-2">Schema</h5>
              <pre class="text-xs text-gray-600 overflow-x-auto">{{ JSON.stringify(tool.inputSchema, null, 2) }}</pre>
            </div>

            <!-- Dynamic Form -->
            <form @submit.prevent="callTool" class="space-y-4">
              <div v-for="(property, key) in inputProperties" :key="key">
                <label :for="key" class="block text-sm font-medium text-gray-700">
                  {{ key }}
                  <span v-if="isRequired(key)" class="text-red-500">*</span>
                </label>
                
                <!-- String/Text Input -->
                <input
                  v-if="property.type === 'string' && !property.enum"
                  :id="key"
                  v-model="formData[key]"
                  :type="property.format === 'password' ? 'password' : 'text'"
                  :required="isRequired(key)"
                  :placeholder="property.description || property.example"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
                
                <!-- Number Input -->
                <input
                  v-else-if="property.type === 'number' || property.type === 'integer'"
                  :id="key"
                  v-model.number="formData[key]"
                  type="number"
                  :required="isRequired(key)"
                  :min="property.minimum"
                  :max="property.maximum"
                  :step="property.type === 'integer' ? 1 : 0.01"
                  :placeholder="property.description || property.example"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
                
                <!-- Boolean Input -->
                <div v-else-if="property.type === 'boolean'" class="mt-1">
                  <input
                    :id="key"
                    v-model="formData[key]"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label :for="key" class="ml-2 text-sm text-gray-600">
                    {{ property.description || 'Enable this option' }}
                  </label>
                </div>
                
                <!-- Enum/Select Input -->
                <select
                  v-else-if="property.enum"
                  :id="key"
                  v-model="formData[key]"
                  :required="isRequired(key)"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select an option</option>
                  <option v-for="option in property.enum" :key="option" :value="option">
                    {{ option }}
                  </option>
                </select>
                
                <!-- Array Input -->
                <div v-else-if="property.type === 'array'" class="space-y-2">
                  <div v-for="(item, index) in (formData[key] || [])" :key="index" class="flex gap-2">
                    <input
                      v-model="formData[key][index]"
                      type="text"
                      class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      :placeholder="`Item ${index + 1}`"
                    />
                    <button
                      type="button"
                      @click="removeArrayItem(key, index)"
                      class="px-3 py-2 text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  </div>
                  <button
                    type="button"
                    @click="addArrayItem(key)"
                    class="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    + Add Item
                  </button>
                </div>
                
                <!-- Object/JSON Input -->
                <textarea
                  v-else-if="property.type === 'object'"
                  :id="key"
                  v-model="objectInputs[key]"
                  rows="3"
                  :required="isRequired(key)"
                  placeholder="Enter JSON object"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
                
                <!-- Fallback Text Input -->
                <input
                  v-else
                  :id="key"
                  v-model="formData[key]"
                  type="text"
                  :required="isRequired(key)"
                  :placeholder="property.description || property.example"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
                
                <!-- Property Description -->
                <p v-if="property.description" class="mt-1 text-xs text-gray-500">
                  {{ property.description }}
                </p>
              </div>

              <!-- Submit Button -->
              <div class="pt-4">
                <button
                  type="submit"
                  :disabled="loading"
                  class="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {{ loading ? 'Calling Tool...' : 'Call Tool' }}
                </button>
              </div>
            </form>
          </div>

          <!-- Output Panel -->
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">Output</h4>
            
            <!-- Loading State -->
            <div v-if="loading" class="flex items-center justify-center p-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
            
            <!-- Error State -->
            <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="flex">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Error</h3>
                  <p class="mt-1 text-sm text-red-700">{{ error }}</p>
                </div>
              </div>
            </div>
            
            <!-- Success State -->
            <div v-else-if="result" class="space-y-4">
              <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex">
                  <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">Success</h3>
                    <p class="mt-1 text-sm text-green-700">Tool executed successfully</p>
                  </div>
                </div>
              </div>
              
              <!-- Result Content -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h5 class="text-sm font-medium text-gray-700 mb-2">Response</h5>
                <div v-if="result.content && result.content.length > 0" class="space-y-2">
                  <div v-for="(content, index) in result.content" :key="index" class="border rounded p-2 bg-white">
                    <div class="flex justify-between items-start mb-2">
                      <span class="text-xs font-medium text-gray-500 uppercase">{{ content.type }}</span>
                    </div>
                    <div v-if="content.type === 'text'" class="text-sm text-gray-900 whitespace-pre-wrap">
                      {{ content.text }}
                    </div>
                    <div v-else class="text-sm text-gray-900">
                      <pre class="overflow-x-auto">{{ JSON.stringify(content.data, null, 2) }}</pre>
                    </div>
                  </div>
                </div>
                <div v-else class="text-sm text-gray-500">
                  No content returned
                </div>
              </div>
            </div>
            
            <!-- Empty State -->
            <div v-else class="text-center p-8 text-gray-500">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p class="mt-2 text-sm">Fill in the parameters and call the tool to see the output</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useMcpStore } from '@/store/mcp'
import type { McpTool, McpToolCallResponse } from '@/types/mcp'

interface Props {
  tool: McpTool
  sandboxId: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

const mcpStore = useMcpStore()
const loading = ref(false)
const error = ref<string | null>(null)
const result = ref<McpToolCallResponse | null>(null)
const formData = reactive<Record<string, any>>({})
const objectInputs = reactive<Record<string, string>>({})

// Extract properties from input schema
const inputProperties = computed(() => {
  return props.tool.inputSchema?.properties || {}
})

const requiredFields = computed(() => {
  return props.tool.inputSchema?.required || []
})

const isRequired = (fieldName: string) => {
  return requiredFields.value.includes(fieldName)
}

// Initialize form data
Object.keys(inputProperties.value).forEach(key => {
  const property = inputProperties.value[key]
  if (property.type === 'array') {
    formData[key] = []
  } else if (property.type === 'object') {
    formData[key] = {}
    objectInputs[key] = '{}'
  } else if (property.type === 'boolean') {
    formData[key] = false
  } else if (property.default !== undefined) {
    formData[key] = property.default
  }
})

// Watch object inputs and parse JSON
watch(objectInputs, (newInputs) => {
  Object.keys(newInputs).forEach(key => {
    try {
      formData[key] = JSON.parse(newInputs[key])
    } catch (e) {
      // Invalid JSON, keep as string for now
    }
  })
}, { deep: true })

const addArrayItem = (key: string) => {
  if (!formData[key]) {
    formData[key] = []
  }
  formData[key].push('')
}

const removeArrayItem = (key: string, index: number) => {
  if (formData[key]) {
    formData[key].splice(index, 1)
  }
}

const callTool = async () => {
  loading.value = true
  error.value = null
  result.value = null

  try {
    // Prepare arguments by filtering out empty values
    const arguments: Record<string, any> = {}
    
    Object.keys(formData).forEach(key => {
      const value = formData[key]
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          // Filter out empty array items
          const filteredArray = value.filter(item => item !== null && item !== undefined && item !== '')
          if (filteredArray.length > 0) {
            arguments[key] = filteredArray
          }
        } else {
          arguments[key] = value
        }
      }
    })

    result.value = await mcpStore.callTool(props.sandboxId, props.tool.name, arguments)
  } catch (err: any) {
    error.value = err.message || 'Failed to call tool'
  } finally {
    loading.value = false
  }
}
</script>
