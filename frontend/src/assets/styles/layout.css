/* 布局样式 */
@layer components {
  /* 页头 */
  .app-header {
    @apply bg-primary text-white py-4 shadow-md;
  }
  
  .header-container {
    @apply container mx-auto px-4 flex justify-between items-center;
  }
  
  /* 页脚 */
  .app-footer {
    @apply bg-gray-800 text-white py-6;
  }
  
  /* 主内容区 */
  .main-content {
    @apply container mx-auto px-4 py-8;
  }
  
  /* 响应式网格 */
  .responsive-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }
}
