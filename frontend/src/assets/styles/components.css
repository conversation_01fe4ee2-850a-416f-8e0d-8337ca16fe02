/* 组件样式 */
@tailwind components;

@layer components {
  /* 按钮 */
  .btn {
    @apply inline-block py-2 px-4 bg-primary text-white rounded cursor-pointer transition-colors;
  }
  
  .btn:hover {
    @apply bg-primary-dark;
  }
  
  .btn-logout {
    @apply bg-white text-primary;
  }
  
  .btn-logout:hover {
    @apply bg-gray-100;
  }
  
  /* 表单控件 */
  .form-control {
    @apply w-full p-2 border border-gray-300 rounded text-base;
  }
  
  .form-control:focus {
    @apply outline-none ring-2 ring-primary ring-opacity-50 border-primary;
  }
  
  .error-message {
    @apply text-error text-sm mt-1;
  }
  
  /* 卡片 */
  .card {
    @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow;
  }
  
  /* 容器 */
  .page-container {
    @apply min-h-screen bg-gray-100;
  }
  
  .content-container {
    @apply container mx-auto px-4 py-8;
  }
}
