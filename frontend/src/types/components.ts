import { Component } from 'vue'

export interface MenuItem {
  label: string;
  path: string;
  icon?: string;
  component?: Component;
}

export interface FormField {
  name: string;
  label: string;
  type: string;
  placeholder?: string;
  required?: boolean;
  validation?: (value: any) => boolean | string;
}

export interface FormConfig {
  fields: FormField[];
  submitLabel: string;
  cancelLabel?: string;
}
