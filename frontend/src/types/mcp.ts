// MCP-related TypeScript types

export interface McpServerConfiguration {
  id?: number;
  name: string;
  description?: string;
  command: string;
  arguments?: string[];
  environment?: Record<string, string>;
  workingDirectory?: string;
  dockerImage: string;
  resourceLimits?: ResourceLimits;
  networkConfig?: NetworkConfig;
  volumeMounts?: VolumeMount[];
  timeoutSeconds?: number;
  autoRestart?: boolean;
  enabled?: boolean;
  userId?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface ResourceLimits {
  memoryLimitBytes?: number;
  cpuLimit?: number;
  diskLimitBytes?: number;
}

export interface NetworkConfig {
  enableNetworking?: boolean;
  allowedHosts?: string[];
  exposedPorts?: number[];
}

export interface VolumeMount {
  hostPath: string;
  containerPath: string;
  readOnly?: boolean;
}

export interface McpServerInstance {
  id?: number;
  configurationId: number;
  sandboxId: string;
  status: SandboxStatus;
  sandboxType: string;
  startedAt?: string;
  stoppedAt?: string;
  errorMessage?: string;
  userId: number;
  createdAt?: string;
  updatedAt?: string;
}

export enum SandboxStatus {
  CREATING = 'CREATING',
  CREATED = 'CREATED',
  STARTING = 'STARTING',
  RUNNING = 'RUNNING',
  STOPPING = 'STOPPING',
  STOPPED = 'STOPPED',
  FAILED = 'FAILED',
  DESTROYING = 'DESTROYING',
  DESTROYED = 'DESTROYED'
}

export interface McpTool {
  name: string;
  description: string;
  inputSchema: Record<string, any>;
  outputSchema?: Record<string, any>;
}

export interface McpServerInfo {
  name: string;
  version: string;
  description?: string;
  author?: string;
  license?: string;
  homepage?: string;
}

export interface McpToolCallResponse {
  content: McpContent[];
  isError?: boolean;
}

export interface McpContent {
  type: string;
  text?: string;
  data?: any;
}

export interface SandboxResourceUsage {
  timestamp: string;
  cpuUsagePercent?: number;
  memoryUsageBytes?: number;
  memoryLimitBytes?: number;
  memoryUsagePercent?: number;
  diskUsageBytes?: number;
  networkBytesReceived?: number;
  networkBytesTransmitted?: number;
  processId?: number;
  processCount?: number;
  uptimeSeconds?: number;
}
