import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import LoginView from '../LoginView.vue'
import { useAuthStore } from '@/store/auth'

// 模拟路由
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/login', name: 'login', component: LoginView }
  ]
})

// 模拟 Pinia 存储
vi.mock('@/store/auth', () => ({
  useAuthStore: vi.fn(() => ({
    login: vi.fn().mockResolvedValue(true),
    register: vi.fn().mockResolvedValue(true),
    error: null,
    loading: false
  }))
}))

describe('LoginView', () => {
  beforeEach(() => {
    // 创建一个新的 Pinia 实例
    setActivePinia(createPinia())

    // 重置路由
    router.push('/login')

    // 清除所有模拟
    vi.clearAllMocks()
  })

  it('renders login form by default', () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })

    // 验证标题
    expect(wrapper.find('h1').text()).toBe('登录')

    // 验证表单字段
    expect(wrapper.find('#username').exists()).toBe(true)
    expect(wrapper.find('#password').exists()).toBe(true)
    expect(wrapper.find('#email').exists()).toBe(false)
    expect(wrapper.find('#fullName').exists()).toBe(false)

    // 验证按钮文本
    expect(wrapper.find('button[type="submit"]').text()).toBe('登录')
  })

  it('toggles between login and register forms', async () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })

    // 初始状态是登录表单
    expect(wrapper.find('h1').text()).toBe('登录')

    // 点击切换链接
    await wrapper.find('.text-center a').trigger('click')

    // 现在应该是注册表单
    expect(wrapper.find('h1').text()).toBe('注册')
    expect(wrapper.find('#email').exists()).toBe(true)
    expect(wrapper.find('#fullName').exists()).toBe(true)

    // 再次点击切换链接
    await wrapper.find('.text-center a').trigger('click')

    // 应该回到登录表单
    expect(wrapper.find('h1').text()).toBe('登录')
  })

  it('submits login form and redirects on success', async () => {
    const mockAuthStore = useAuthStore()
    const mockRouter = { push: vi.fn() }

    const wrapper = mount(LoginView, {
      global: {
        plugins: [router],
        mocks: {
          $router: mockRouter
        }
      }
    })

    // 填写表单
    await wrapper.find('#username').setValue('testuser')
    await wrapper.find('#password').setValue('password')

    // 提交表单
    await wrapper.find('form').trigger('submit.prevent')

    // 验证 store 方法被调用
    expect(mockAuthStore.login).toHaveBeenCalledWith('testuser', 'password')

    // 验证路由跳转
    expect(mockRouter.push).toHaveBeenCalledWith('/')
  })

  it('submits register form and switches to login on success', async () => {
    const mockAuthStore = useAuthStore()

    const wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })

    // 切换到注册表单
    await wrapper.find('.text-center a').trigger('click')

    // 填写表单
    await wrapper.find('#username').setValue('newuser')
    await wrapper.find('#email').setValue('<EMAIL>')
    await wrapper.find('#password').setValue('password')
    await wrapper.find('#fullName').setValue('New User')

    // 提交表单
    await wrapper.find('form').trigger('submit.prevent')

    // 验证 store 方法被调用
    expect(mockAuthStore.register).toHaveBeenCalledWith({
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password',
      fullName: 'New User'
    })

    // 验证切换回登录表单
    expect(wrapper.find('h1').text()).toBe('登录')
  })
})
