import { defineStore } from 'pinia'
import axios from 'axios'
import { User, LoginResponse, RegisterRequest } from '@/types/user'

interface AuthState {
  token: string | null;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: localStorage.getItem('token') || null,
    user: null,
    loading: false,
    error: null
  }),
  
  getters: {
    isAuthenticated: (state): boolean => !!state.token
  },
  
  actions: {
    async login(username: string, password: string): Promise<boolean> {
      this.loading = true
      this.error = null
      
      try {
        const response = await axios.post<LoginResponse>('/auth/login', { username, password })
        this.token = response.data.token
        localStorage.setItem('token', this.token)
        
        // Configure axios to use the token for future requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`
        
        return true
      } catch (error: any) {
        this.error = error.response?.data?.error || '登录失败'
        return false
      } finally {
        this.loading = false
      }
    },
    
    logout(): void {
      this.token = null
      this.user = null
      localStorage.removeItem('token')
      delete axios.defaults.headers.common['Authorization']
    },
    
    async register(userData: RegisterRequest): Promise<boolean> {
      this.loading = true
      this.error = null
      
      try {
        await axios.post('/auth/register', userData)
        return true
      } catch (error: any) {
        this.error = error.response?.data?.error || '注册失败'
        return false
      } finally {
        this.loading = false
      }
    }
  }
})
