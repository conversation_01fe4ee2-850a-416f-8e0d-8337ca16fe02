# 测试环境配置

# R2DBC 测试数据库配置
spring.r2dbc.url=r2dbc:h2:mem:///testdb;DB_CLOSE_DELAY=-1
spring.r2dbc.username=sa
spring.r2dbc.password=password

# JDBC 测试数据库配置
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# 禁用 H2 控制台
spring.h2.console.enabled=false

# JWT 配置
jwt.secret=testSecretKeyThatIsLongEnoughForHS256Algorithm
jwt.expiration=3600000
