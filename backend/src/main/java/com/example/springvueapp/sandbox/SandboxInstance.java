package com.example.springvueapp.sandbox;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;

/**
 * Represents a running sandbox instance
 */
public interface SandboxInstance {
    
    /**
     * Get the unique identifier for this sandbox
     * @return Sandbox ID
     */
    String getId();
    
    /**
     * Get the sandbox configuration
     * @return Configuration used to create this sandbox
     */
    SandboxConfig getConfig();
    
    /**
     * Get the current status of the sandbox
     * @return Current status
     */
    SandboxStatus getStatus();
    
    /**
     * Get the creation time of this sandbox
     * @return Creation timestamp
     */
    LocalDateTime getCreatedAt();
    
    /**
     * Start the MCP server process in the sandbox
     * @return Completion signal
     */
    Mono<Void> start();
    
    /**
     * Stop the MCP server process
     * @return Completion signal
     */
    Mono<Void> stop();
    
    /**
     * Restart the MCP server process
     * @return Completion signal
     */
    Mono<Void> restart();
    
    /**
     * Get the input stream to write to the MCP server's stdin
     * @return Output stream for writing to server
     */
    OutputStream getStdinStream();
    
    /**
     * Get the output stream to read from the MCP server's stdout
     * @return Input stream for reading from server
     */
    InputStream getStdoutStream();
    
    /**
     * Get the error stream to read from the MCP server's stderr
     * @return Input stream for reading errors
     */
    InputStream getStderrStream();
    
    /**
     * Get a reactive stream of stdout data
     * @return Flux of stdout data
     */
    Flux<String> getStdoutFlux();
    
    /**
     * Get a reactive stream of stderr data
     * @return Flux of stderr data
     */
    Flux<String> getStderrFlux();
    
    /**
     * Write data to the MCP server's stdin
     * @param data Data to write
     * @return Completion signal
     */
    Mono<Void> writeToStdin(String data);
    
    /**
     * Check if the sandbox is running
     * @return True if running, false otherwise
     */
    boolean isRunning();
    
    /**
     * Get resource usage statistics
     * @return Resource usage information
     */
    Mono<SandboxResourceUsage> getResourceUsage();
    
    /**
     * Destroy this sandbox instance
     * @return Completion signal
     */
    Mono<Void> destroy();
}
