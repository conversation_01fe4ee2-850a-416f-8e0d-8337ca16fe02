package com.example.springvueapp.sandbox;

import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Abstract interface for sandbox environments that can run MCP servers
 */
public interface SandboxEnvironment {
    
    /**
     * Create a new sandbox instance
     * @param config Sandbox configuration
     * @return Sandbox instance
     */
    Mono<SandboxInstance> createSandbox(SandboxConfig config);
    
    /**
     * Get an existing sandbox instance by ID
     * @param sandboxId Sandbox identifier
     * @return Sandbox instance or empty if not found
     */
    Mono<SandboxInstance> getSandbox(String sandboxId);
    
    /**
     * List all active sandbox instances
     * @return Map of sandbox ID to instance
     */
    Mono<Map<String, SandboxInstance>> listSandboxes();
    
    /**
     * Destroy a sandbox instance
     * @param sandboxId Sandbox identifier
     * @return Completion signal
     */
    Mono<Void> destroySandbox(String sandboxId);
    
    /**
     * Cleanup all sandbox instances
     * @return Completion signal
     */
    Mono<Void> cleanup();
    
    /**
     * Get the type of this sandbox environment
     * @return Environment type (e.g., "docker", "kubernetes", "local")
     */
    String getType();
    
    /**
     * Check if this sandbox environment is available/healthy
     * @return Health status
     */
    Mono<Boolean> isHealthy();
}
