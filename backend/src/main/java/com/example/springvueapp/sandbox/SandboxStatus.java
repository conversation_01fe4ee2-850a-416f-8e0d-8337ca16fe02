package com.example.springvueapp.sandbox;

/**
 * Enumeration of possible sandbox states
 */
public enum SandboxStatus {
    /**
     * Sandbox is being created
     */
    CREATING,
    
    /**
     * Sandbox is created but not started
     */
    CREATED,
    
    /**
     * Sandbox is starting up
     */
    STARTING,
    
    /**
     * Sandbox is running normally
     */
    RUNNING,
    
    /**
     * Sandbox is stopping
     */
    STOPPING,
    
    /**
     * Sandbox has stopped
     */
    STOPPED,
    
    /**
     * Sandbox has failed
     */
    FAILED,
    
    /**
     * Sandbox is being destroyed
     */
    DESTROYING,
    
    /**
     * Sandbox has been destroyed
     */
    DESTROYED
}
