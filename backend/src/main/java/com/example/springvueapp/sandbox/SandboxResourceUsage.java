package com.example.springvueapp.sandbox;

import java.time.LocalDateTime;

/**
 * 沙箱实例的资源使用统计
 */
public class SandboxResourceUsage {

    /**
     * 收集这些指标的时间戳
     */
    private LocalDateTime timestamp;

    /**
     * CPU 使用百分比 (0.0 到 100.0)
     */
    private Double cpuUsagePercent;

    /**
     * 内存使用量（字节）
     */
    private Long memoryUsageBytes;

    /**
     * 内存限制（字节）
     */
    private Long memoryLimitBytes;

    /**
     * 内存使用百分比 (0.0 到 100.0)
     */
    private Double memoryUsagePercent;

    /**
     * 磁盘使用量（字节）
     */
    private Long diskUsageBytes;

    /**
     * 网络接收字节数
     */
    private Long networkBytesReceived;

    /**
     * 网络传输字节数
     */
    private Long networkBytesTransmitted;

    /**
     * 主进程的进程ID
     */
    private Long processId;

    /**
     * 运行中的进程数
     */
    private Integer processCount;

    /**
     * 运行时间（秒）
     */
    private Long uptimeSeconds;

    public SandboxResourceUsage() {
    }

    // Getters and Setters
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }

    public Double getCpuUsagePercent() { return cpuUsagePercent; }
    public void setCpuUsagePercent(Double cpuUsagePercent) { this.cpuUsagePercent = cpuUsagePercent; }

    public Long getMemoryUsageBytes() { return memoryUsageBytes; }
    public void setMemoryUsageBytes(Long memoryUsageBytes) { this.memoryUsageBytes = memoryUsageBytes; }

    public Long getMemoryLimitBytes() { return memoryLimitBytes; }
    public void setMemoryLimitBytes(Long memoryLimitBytes) { this.memoryLimitBytes = memoryLimitBytes; }

    public Double getMemoryUsagePercent() { return memoryUsagePercent; }
    public void setMemoryUsagePercent(Double memoryUsagePercent) { this.memoryUsagePercent = memoryUsagePercent; }

    public Long getDiskUsageBytes() { return diskUsageBytes; }
    public void setDiskUsageBytes(Long diskUsageBytes) { this.diskUsageBytes = diskUsageBytes; }

    public Long getNetworkBytesReceived() { return networkBytesReceived; }
    public void setNetworkBytesReceived(Long networkBytesReceived) { this.networkBytesReceived = networkBytesReceived; }

    public Long getNetworkBytesTransmitted() { return networkBytesTransmitted; }
    public void setNetworkBytesTransmitted(Long networkBytesTransmitted) { this.networkBytesTransmitted = networkBytesTransmitted; }

    public Long getProcessId() { return processId; }
    public void setProcessId(Long processId) { this.processId = processId; }

    public Integer getProcessCount() { return processCount; }
    public void setProcessCount(Integer processCount) { this.processCount = processCount; }

    public Long getUptimeSeconds() { return uptimeSeconds; }
    public void setUptimeSeconds(Long uptimeSeconds) { this.uptimeSeconds = uptimeSeconds; }

    /**
     * SandboxResourceUsage 的构建器类
     */
    public static class Builder {
        private SandboxResourceUsage usage = new SandboxResourceUsage();

        public Builder timestamp(LocalDateTime timestamp) {
            usage.setTimestamp(timestamp);
            return this;
        }

        public Builder cpuUsagePercent(Double cpuUsagePercent) {
            usage.setCpuUsagePercent(cpuUsagePercent);
            return this;
        }

        public Builder memoryUsageBytes(Long memoryUsageBytes) {
            usage.setMemoryUsageBytes(memoryUsageBytes);
            return this;
        }

        public Builder memoryLimitBytes(Long memoryLimitBytes) {
            usage.setMemoryLimitBytes(memoryLimitBytes);
            return this;
        }

        public Builder memoryUsagePercent(Double memoryUsagePercent) {
            usage.setMemoryUsagePercent(memoryUsagePercent);
            return this;
        }

        public Builder diskUsageBytes(Long diskUsageBytes) {
            usage.setDiskUsageBytes(diskUsageBytes);
            return this;
        }

        public Builder networkBytesReceived(Long networkBytesReceived) {
            usage.setNetworkBytesReceived(networkBytesReceived);
            return this;
        }

        public Builder networkBytesTransmitted(Long networkBytesTransmitted) {
            usage.setNetworkBytesTransmitted(networkBytesTransmitted);
            return this;
        }

        public Builder processId(Long processId) {
            usage.setProcessId(processId);
            return this;
        }

        public Builder processCount(Integer processCount) {
            usage.setProcessCount(processCount);
            return this;
        }

        public Builder uptimeSeconds(Long uptimeSeconds) {
            usage.setUptimeSeconds(uptimeSeconds);
            return this;
        }

        public SandboxResourceUsage build() {
            return usage;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
