package com.example.springvueapp.controller;

import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.service.McpConfigurationService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 管理 MCP 服务器配置的 REST 控制器
 */
@RestController
@RequestMapping("/api/mcp/configurations")
public class McpConfigurationController {

    private static final Logger log = LoggerFactory.getLogger(McpConfigurationController.class);

    private final McpConfigurationService configurationService;

    public McpConfigurationController(McpConfigurationService configurationService) {
        this.configurationService = configurationService;
    }

    /**
     * Get all configurations for the authenticated user
     */
    @GetMapping
    public Flux<McpServerConfiguration> getUserConfigurations(Authentication authentication) {
        Long userId = getUserId(authentication);
        return configurationService.getUserConfigurations(userId);
    }

    /**
     * Get enabled configurations for the authenticated user
     */
    @GetMapping("/enabled")
    public Flux<McpServerConfiguration> getEnabledConfigurations(Authentication authentication) {
        Long userId = getUserId(authentication);
        return configurationService.getEnabledConfigurations(userId);
    }

    /**
     * Get a specific configuration by ID
     */
    @GetMapping("/{id}")
    public Mono<McpServerConfiguration> getConfiguration(@PathVariable Long id, Authentication authentication) {
        Long userId = getUserId(authentication);
        return configurationService.getConfiguration(id, userId);
    }

    /**
     * Create a new MCP server configuration
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public Mono<McpServerConfiguration> createConfiguration(
            @Valid @RequestBody McpServerConfiguration config,
            Authentication authentication) {
        Long userId = getUserId(authentication);
        return configurationService.createConfiguration(config, userId);
    }

    /**
     * Update an existing MCP server configuration
     */
    @PutMapping("/{id}")
    public Mono<McpServerConfiguration> updateConfiguration(
            @PathVariable Long id,
            @Valid @RequestBody McpServerConfiguration config,
            Authentication authentication) {
        Long userId = getUserId(authentication);
        return configurationService.updateConfiguration(id, config, userId);
    }

    /**
     * Delete an MCP server configuration
     */
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public Mono<Void> deleteConfiguration(@PathVariable Long id, Authentication authentication) {
        Long userId = getUserId(authentication);
        return configurationService.deleteConfiguration(id, userId);
    }

    /**
     * Toggle enabled status of a configuration
     */
    @PostMapping("/{id}/toggle")
    public Mono<McpServerConfiguration> toggleEnabled(@PathVariable Long id, Authentication authentication) {
        Long userId = getUserId(authentication);
        return configurationService.toggleEnabled(id, userId);
    }

    private Long getUserId(Authentication authentication) {
        // Extract user ID from authentication - this would depend on your auth implementation
        // For now, returning a placeholder
        return 1L; // TODO: Implement proper user ID extraction
    }
}
