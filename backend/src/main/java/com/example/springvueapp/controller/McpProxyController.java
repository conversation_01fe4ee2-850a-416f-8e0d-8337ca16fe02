package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.model.*;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.sandbox.SandboxResourceUsage;
import com.example.springvueapp.service.McpConfigurationService;
import com.example.springvueapp.service.McpProxyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * MCP 代理操作的 REST 控制器
 */
@RestController
@RequestMapping("/api/mcp/proxy")
public class McpProxyController {

    private static final Logger log = LoggerFactory.getLogger(McpProxyController.class);

    private final McpProxyService proxyService;
    private final McpConfigurationService configurationService;

    public McpProxyController(McpProxyService proxyService, McpConfigurationService configurationService) {
        this.proxyService = proxyService;
        this.configurationService = configurationService;
    }

    /**
     * Start an MCP server instance
     */
    @PostMapping("/start/{configId}")
    @ResponseStatus(HttpStatus.CREATED)
    public Mono<McpServerInstance> startServer(@PathVariable Long configId, Authentication authentication) {
        Long userId = getUserId(authentication);

        return configurationService.getConfiguration(configId, userId)
                .flatMap(config -> proxyService.startServer(config, userId));
    }

    /**
     * Stop an MCP server instance
     */
    @PostMapping("/stop/{sandboxId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public Mono<Void> stopServer(@PathVariable String sandboxId) {
        return proxyService.stopServer(sandboxId);
    }

    /**
     * Get all running instances for the authenticated user
     */
    @GetMapping("/instances")
    public Flux<McpServerInstance> getRunningInstances(Authentication authentication) {
        Long userId = getUserId(authentication);
        return proxyService.getRunningInstances(userId);
    }

    /**
     * Get instance status
     */
    @GetMapping("/instances/{sandboxId}/status")
    public Mono<McpServerInstance> getInstanceStatus(@PathVariable String sandboxId) {
        return proxyService.getInstanceStatus(sandboxId);
    }

    /**
     * Get resource usage for an instance
     */
    @GetMapping("/instances/{sandboxId}/resources")
    public Mono<SandboxResourceUsage> getResourceUsage(@PathVariable String sandboxId) {
        return proxyService.getResourceUsage(sandboxId);
    }

    /**
     * Get available tools from an MCP server
     */
    @GetMapping("/{sandboxId}/tools")
    public Mono<List<McpTool>> getTools(@PathVariable String sandboxId) {
        return proxyService.getTools(sandboxId);
    }

    /**
     * Get server information
     */
    @GetMapping("/{sandboxId}/info")
    public Mono<McpServerInfo> getServerInfo(@PathVariable String sandboxId) {
        return proxyService.getServerInfo(sandboxId);
    }

    /**
     * Call a tool on an MCP server
     */
    @PostMapping("/{sandboxId}/tools/{toolName}")
    public Mono<McpToolCallResponse> callTool(
            @PathVariable String sandboxId,
            @PathVariable String toolName,
            @RequestBody Map<String, Object> arguments) {

        return proxyService.callTool(sandboxId, toolName, arguments);
    }

    /**
     * Generic HTTP-to-MCP proxy endpoint
     * This endpoint converts any HTTP request to the corresponding MCP tool call
     */
    @PostMapping("/{sandboxId}/http/{toolName}")
    public Mono<Object> httpToMcpProxy(
            @PathVariable String sandboxId,
            @PathVariable String toolName,
            @RequestBody(required = false) Map<String, Object> requestBody,
            @RequestParam Map<String, String> queryParams) {

        // Merge query params and request body
        Map<String, Object> arguments = Map.of();
        if (requestBody != null) {
            arguments.putAll(requestBody);
        }
        arguments.putAll(queryParams);

        return proxyService.callTool(sandboxId, toolName, arguments)
                .map(response -> {
                    // Convert MCP response to HTTP response
                    if (response.getIsError() != null && response.getIsError()) {
                        throw new RuntimeException("Tool execution failed");
                    }

                    // Extract content from MCP response
                    if (response.getContent() != null && !response.getContent().isEmpty()) {
                        McpToolCallResponse.McpContent content = response.getContent().get(0);
                        if ("text".equals(content.getType())) {
                            return content.getText();
                        } else {
                            return content.getData();
                        }
                    }

                    return response;
                });
    }

    private Long getUserId(Authentication authentication) {
        // Extract user ID from authentication - this would depend on your auth implementation
        // For now, returning a placeholder
        return 1L; // TODO: Implement proper user ID extraction
    }
}
