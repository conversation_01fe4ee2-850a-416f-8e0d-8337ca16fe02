package com.example.springvueapp.controller;

import com.example.springvueapp.model.User;
import com.example.springvueapp.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public Mono<ResponseEntity<Map<String, String>>> authenticateUser(@RequestBody Map<String, String> loginRequest) {
        return authService.authenticateUser(
                loginRequest.get("username"),
                loginRequest.get("password")
            )
            .map(token -> {
                Map<String, String> response = new HashMap<>();
                response.put("token", token);
                return ResponseEntity.ok(response);
            })
            .onErrorResume(e -> Mono.just(ResponseEntity.badRequest().body(Map.of("error", e.getMessage()))));
    }

    @PostMapping("/register")
    public Mono<ResponseEntity<?>> registerUser(@RequestBody User user) {
        return authService.registerUser(user)
            .flatMap(success -> {
                if (success) {
                    return Mono.just(ResponseEntity.ok().build());
                } else {
                    return Mono.just(ResponseEntity.badRequest().body(Map.of("error", "用户名或邮箱已存在")));
                }
            });
    }
}
