package com.example.springvueapp.service;

import com.example.springvueapp.model.User;
import com.example.springvueapp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class AuthService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtService jwtService;

    public Mono<String> authenticateUser(String username, String password) {
        return userRepository.findByUsername(username)
            .filter(user -> passwordEncoder.matches(password, user.getPassword()))
            .switchIfEmpty(Mono.error(new IllegalArgumentException("用户名或密码错误")))
            .flatMap(user -> jwtService.generateTokenMono(user.getUsername()));
    }

    public Mono<Boolean> registerUser(User user) {
        return Mono.zip(
                userRepository.existsByUsername(user.getUsername()),
                userRepository.existsByEmail(user.getEmail())
            )
            .filter(tuple -> !tuple.getT1() && !tuple.getT2())
            .switchIfEmpty(Mono.error(new RuntimeException("Missing user")))
            .flatMap(tuple -> {
                user.setPassword(passwordEncoder.encode(user.getPassword()));
                return userRepository.save(user).map(savedUser -> true);
            })
            .onErrorReturn(false);
    }
}
