package com.example.springvueapp.mcp.client;

import com.example.springvueapp.mcp.protocol.JsonRpcError;

/**
 * Exception thrown when MCP operations fail
 */
public class McpException extends RuntimeException {
    
    private final JsonRpcError error;
    
    public McpException(String message) {
        super(message);
        this.error = null;
    }
    
    public McpException(String message, Throwable cause) {
        super(message, cause);
        this.error = null;
    }
    
    public McpException(String message, JsonRpcError error) {
        super(message);
        this.error = error;
    }
    
    public JsonRpcError getError() {
        return error;
    }
    
    public int getErrorCode() {
        return error != null ? error.getCode() : -1;
    }
}
