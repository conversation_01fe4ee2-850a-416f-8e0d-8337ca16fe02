package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

/**
 * MCP Initialize response
 */
public class McpInitializeResponse {
    
    @JsonProperty("protocolVersion")
    private String protocolVersion;
    
    @JsonProperty("capabilities")
    private Map<String, Object> capabilities;
    
    @JsonProperty("serverInfo")
    private McpServerInfo serverInfo;

    // 无参构造方法
    public McpInitializeResponse() {
    }

    // 全参构造方法
    public McpInitializeResponse(String protocolVersion, Map<String, Object> capabilities, McpServerInfo serverInfo) {
        this.protocolVersion = protocolVersion;
        this.capabilities = capabilities;
        this.serverInfo = serverInfo;
    }

    // Getter 方法
    public String getProtocolVersion() {
        return protocolVersion;
    }

    public Map<String, Object> getCapabilities() {
        return capabilities;
    }

    public McpServerInfo getServerInfo() {
        return serverInfo;
    }

    // Setter 方法
    public void setProtocolVersion(String protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    public void setCapabilities(Map<String, Object> capabilities) {
        this.capabilities = capabilities;
    }

    public void setServerInfo(McpServerInfo serverInfo) {
        this.serverInfo = serverInfo;
    }
}
