package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * MCP 初始化请求参数
 */
public class McpInitializeRequest {

    @JsonProperty("protocolVersion")
    private String protocolVersion;

    @JsonProperty("capabilities")
    private Map<String, Object> capabilities;

    @JsonProperty("clientInfo")
    private McpClientInfo clientInfo;

    public McpInitializeRequest() {
    }

    public McpInitializeRequest(String protocolVersion, Map<String, Object> capabilities, McpClientInfo clientInfo) {
        this.protocolVersion = protocolVersion;
        this.capabilities = capabilities;
        this.clientInfo = clientInfo;
    }

    public String getProtocolVersion() {
        return protocolVersion;
    }

    public void setProtocolVersion(String protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    public Map<String, Object> getCapabilities() {
        return capabilities;
    }

    public void setCapabilities(Map<String, Object> capabilities) {
        this.capabilities = capabilities;
    }

    public McpClientInfo getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(McpClientInfo clientInfo) {
        this.clientInfo = clientInfo;
    }

    public static class McpClientInfo {
        @JsonProperty("name")
        private String name;

        @JsonProperty("version")
        private String version;

        public McpClientInfo() {
        }

        public McpClientInfo(String name, String version) {
            this.name = name;
            this.version = version;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }
}
