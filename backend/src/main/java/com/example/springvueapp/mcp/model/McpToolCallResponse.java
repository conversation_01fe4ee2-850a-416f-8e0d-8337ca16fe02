package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * MCP Tool call response
 */
public class McpToolCallResponse {
    
    @JsonProperty("content")
    private List<McpContent> content;
    
    @JsonProperty("isError")
    private Boolean isError;

    public McpToolCallResponse() {
    }

    public McpToolCallResponse(List<McpContent> content, Boolean isError) {
        this.content = content;
        this.isError = isError;
    }

    public List<McpContent> getContent() {
        return content;
    }

    public void setContent(List<McpContent> content) {
        this.content = content;
    }

    public Boolean getIsError() {
        return isError;
    }

    public void setIsError(Boolean error) {
        isError = error;
    }

    public static class McpContent {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("text")
        private String text;
        
        @JsonProperty("data")
        private Object data;

        public McpContent() {
        }

        public McpContent(String type, String text, Object data) {
            this.type = type;
            this.text = text;
            this.data = data;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }
    }
}
