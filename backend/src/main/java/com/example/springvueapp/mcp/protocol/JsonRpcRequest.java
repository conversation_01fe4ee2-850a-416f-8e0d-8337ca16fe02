package com.example.springvueapp.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * JSON-RPC 2.0 请求消息
 */
public class JsonRpcRequest extends JsonRpcMessage {

    @JsonProperty("method")
    private String method;

    @JsonProperty("params")
    private Object params;

    public JsonRpcRequest() {
        super();
    }

    public JsonRpcRequest(String method, Object params) {
        super();
        this.method = method;
        this.params = params;
    }

    public JsonRpcRequest(Object id, String method, Object params) {
        super(id);
        this.method = method;
        this.params = params;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Object getParams() {
        return params;
    }

    public void setParams(Object params) {
        this.params = params;
    }
}
