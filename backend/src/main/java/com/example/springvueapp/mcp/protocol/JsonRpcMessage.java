package com.example.springvueapp.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * JSON-RPC 2.0 消息基类
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class JsonRpcMessage {

    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";

    @JsonProperty("id")
    private Object id;

    public JsonRpcMessage() {
    }

    public JsonRpcMessage(Object id) {
        this.id = id;
    }

    public JsonRpcMessage(String jsonrpc, Object id) {
        this.jsonrpc = jsonrpc;
        this.id = id;
    }

    public String getJsonrpc() {
        return jsonrpc;
    }

    public void setJsonrpc(String jsonrpc) {
        this.jsonrpc = jsonrpc;
    }

    public Object getId() {
        return id;
    }

    public void setId(Object id) {
        this.id = id;
    }
}
