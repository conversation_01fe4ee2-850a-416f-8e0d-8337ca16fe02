package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP 服务器信息
 */
public class McpServerInfo {

    @JsonProperty("name")
    private String name;

    @JsonProperty("version")
    private String version;

    @JsonProperty("description")
    private String description;

    @JsonProperty("author")
    private String author;

    @JsonProperty("license")
    private String license;

    @JsonProperty("homepage")
    private String homepage;

    public McpServerInfo() {
    }

    public McpServerInfo(String name, String version, String description, String author, String license, String homepage) {
        this.name = name;
        this.version = version;
        this.description = description;
        this.author = author;
        this.license = license;
        this.homepage = homepage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getHomepage() {
        return homepage;
    }

    public void setHomepage(String homepage) {
        this.homepage = homepage;
    }
}
