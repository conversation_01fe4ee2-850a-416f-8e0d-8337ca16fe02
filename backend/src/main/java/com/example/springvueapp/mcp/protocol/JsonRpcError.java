package com.example.springvueapp.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * JSON-RPC 2.0 错误对象
 */
public class JsonRpcError {

    @JsonProperty("code")
    private int code;

    @JsonProperty("message")
    private String message;

    @JsonProperty("data")
    private Object data;

    // 标准 JSON-RPC 错误代码
    public static final int PARSE_ERROR = -32700;
    public static final int INVALID_REQUEST = -32600;
    public static final int METHOD_NOT_FOUND = -32601;
    public static final int INVALID_PARAMS = -32602;
    public static final int INTERNAL_ERROR = -32603;

    // MCP 特定错误代码
    public static final int MCP_SERVER_ERROR = -32000;
    public static final int MCP_TOOL_NOT_FOUND = -32001;
    public static final int MCP_TOOL_EXECUTION_ERROR = -32002;

    public JsonRpcError() {
    }

    public JsonRpcError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public JsonRpcError(int code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
