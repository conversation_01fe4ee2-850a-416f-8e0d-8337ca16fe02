package com.example.springvueapp.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * JSON-RPC 2.0 响应消息
 */
public class JsonRpcResponse extends JsonRpcMessage {

    @JsonProperty("result")
    private Object result;

    @JsonProperty("error")
    private JsonRpcError error;

    public JsonRpcResponse() {
        super();
    }

    public JsonRpcResponse(Object id, Object result, JsonRpcError error) {
        super(id);
        this.result = result;
        this.error = error;
    }

    public JsonRpcResponse(Object id, Object result) {
        super(id);
        this.result = result;
    }

    public JsonRpcResponse(Object id, JsonRpcError error) {
        super(id);
        this.error = error;
    }

    public Object getResult() {
        return result;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public JsonRpcError getError() {
        return error;
    }

    public void setError(JsonRpcError error) {
        this.error = error;
    }
}
