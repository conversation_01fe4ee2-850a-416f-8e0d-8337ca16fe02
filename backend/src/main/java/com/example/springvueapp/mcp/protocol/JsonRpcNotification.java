package com.example.springvueapp.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * JSON-RPC 2.0 通知消息（无id字段）
 */
public class JsonRpcNotification {

    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";

    @JsonProperty("method")
    private String method;

    @JsonProperty("params")
    private Object params;

    public JsonRpcNotification() {
    }

    public JsonRpcNotification(String method, Object params) {
        this.method = method;
        this.params = params;
    }

    public JsonRpcNotification(String jsonrpc, String method, Object params) {
        this.jsonrpc = jsonrpc;
        this.method = method;
        this.params = params;
    }

    public String getJsonrpc() {
        return jsonrpc;
    }

    public void setJsonrpc(String jsonrpc) {
        this.jsonrpc = jsonrpc;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Object getParams() {
        return params;
    }

    public void setParams(Object params) {
        this.params = params;
    }
}
