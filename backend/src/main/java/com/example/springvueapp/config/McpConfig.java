package com.example.springvueapp.config;

import com.example.springvueapp.sandbox.SandboxEnvironment;
import com.example.springvueapp.sandbox.docker.DockerSandboxEnvironment;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for MCP-related beans
 */
@Configuration
public class McpConfig {
    
    /**
     * Configure Docker sandbox environment as the default
     */
    @Bean
    @ConditionalOnProperty(name = "mcp.sandbox.type", havingValue = "docker", matchIfMissing = true)
    public SandboxEnvironment dockerSandboxEnvironment() {
        return new DockerSandboxEnvironment();
    }
    
    // Future sandbox implementations can be added here
    // @Bean
    // @ConditionalOnProperty(name = "mcp.sandbox.type", havingValue = "kubernetes")
    // public SandboxEnvironment kubernetesSandboxEnvironment() {
    //     return new KubernetesSandboxEnvironment();
    // }
}
