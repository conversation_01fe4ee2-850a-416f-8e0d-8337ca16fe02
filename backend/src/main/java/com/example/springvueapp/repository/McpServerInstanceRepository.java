package com.example.springvueapp.repository;

import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.sandbox.SandboxStatus;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Repository for MCP server instances
 */
@Repository
public interface McpServerInstanceRepository extends R2dbcRepository<McpServerInstance, Long> {
    
    /**
     * Find instances by user ID
     */
    Flux<McpServerInstance> findByUserId(Long userId);
    
    /**
     * Find instances by configuration ID
     */
    Flux<McpServerInstance> findByConfigurationId(Long configurationId);
    
    /**
     * Find instances by status
     */
    Flux<McpServerInstance> findByStatus(SandboxStatus status);
    
    /**
     * Find instance by sandbox ID
     */
    Mono<McpServerInstance> findBySandboxId(String sandboxId);
    
    /**
     * Find running instances by user ID
     */
    Flux<McpServerInstance> findByUserIdAndStatus(Long userId, SandboxStatus status);
}
