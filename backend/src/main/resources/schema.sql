CREATE TABLE IF NOT EXISTS users (
  id IDENTITY PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(100) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  full_name VARCHAR(100),
  enabled BOOLEAN DEFAULT TRUE
);

-- MCP Server Configurations table
CREATE TABLE IF NOT EXISTS mcp_server_configurations (
    id IDENTITY PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description CLOB,
    command VARCHAR(500) NOT NULL,
    arguments CLOB, -- JSON string of List<String>
    environment CLOB, -- JSO<PERSON> string of Map<String, String>
    working_directory VARCHAR(500),
    docker_image VARCHAR(200) NOT NULL,
    resource_limits CLOB, -- JSON string of ResourceLimits
    network_config CLOB, -- JSON string of NetworkConfig
    volume_mounts CLOB, -- JSON string of List<VolumeMount>
    timeout_seconds INTEGER DEFAULT 300,
    auto_restart BOOLEAN DEFAULT FALSE,
    enabled BOOLEAN DEFAULT TRUE,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- MCP Server Instances table
CREATE TABLE IF NOT EXISTS mcp_server_instances (
    id IDENTITY PRIMARY KEY,
    configuration_id BIGINT NOT NULL,
    sandbox_id VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL,
    sandbox_type VARCHAR(20) NOT NULL,
    started_at TIMESTAMP NULL,
    stopped_at TIMESTAMP NULL,
    error_message CLOB,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (configuration_id) REFERENCES mcp_server_configurations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
