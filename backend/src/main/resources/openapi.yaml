openapi: 3.0.3
info:
  title: Spring Boot WebFlux + Vue 3 应用 API
  description: 基于 Spring Boot WebFlux 和 Vue 3 的全栈应用 API 文档
  version: 1.0.0
  contact:
    name: 开发团队
    email: <EMAIL>
    url: https://example.com
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080
    description: 本地开发环境
  - url: https://api.example.com
    description: 生产环境

tags:
  - name: 认证
    description: 用户认证和注册相关接口
  - name: 用户
    description: 用户管理相关接口

paths:
  /auth/login:
    post:
      tags:
        - 认证
      summary: 用户登录
      description: 使用用户名和密码进行登录，返回JWT令牌
      operationId: login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: 用户名
                  example: zhangsan
                password:
                  type: string
                  description: 密码
                  example: password123
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT令牌
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        '400':
          description: 登录失败
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    description: 错误信息
                    example: 用户名或密码错误

  /auth/register:
    post:
      tags:
        - 认证
      summary: 用户注册
      description: 注册新用户
      operationId: register
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: 注册成功
        '400':
          description: 注册失败，用户名或邮箱已存在
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    description: 错误信息
                    example: 用户名或邮箱已存在

  /users/{username}:
    get:
      tags:
        - 用户
      summary: 根据用户名查找用户
      description: 通过用户名查找用户信息
      operationId: getUserByUsername
      parameters:
        - name: username
          in: path
          description: 用户名
          required: true
          schema:
            type: string
            example: zhangsan
      responses:
        '200':
          description: 查找成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          description: 用户不存在

  /users/search:
    get:
      tags:
        - 用户
      summary: 搜索用户
      description: 根据关键字和状态搜索用户
      operationId: searchUsers
      parameters:
        - name: keyword
          in: query
          description: 搜索关键字
          required: true
          schema:
            type: string
            example: zhang
        - name: activeOnly
          in: query
          description: 是否只查询激活用户
          required: false
          schema:
            type: boolean
            default: false
            example: true
      responses:
        '200':
          description: 搜索成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'

  /users:
    post:
      tags:
        - 用户
      summary: 创建或更新用户
      description: 创建新用户或更新现有用户信息
      operationId: createOrUpdateUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

  /users/with-transaction:
    post:
      tags:
        - 用户
      summary: 使用事务创建用户
      description: 在事务中创建用户
      operationId: createUserWithTransaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

components:
  schemas:
    User:
      type: object
      description: 用户信息
      required:
        - username
        - password
        - email
      properties:
        id:
          type: integer
          format: int64
          description: 用户ID
          example: 1
        username:
          type: string
          description: 用户名
          example: zhangsan
        password:
          type: string
          description: 密码
          example: password123
        email:
          type: string
          description: 电子邮箱
          example: <EMAIL>
        fullName:
          type: string
          description: 姓名
          example: 张三
        enabled:
          type: boolean
          description: 是否启用
          default: true
          example: true

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT 认证令牌

security:
  - bearerAuth: []
